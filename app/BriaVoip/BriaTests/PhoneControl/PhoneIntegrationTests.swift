//
//  PhoneIntegrationTests.swift
//  BriaVoip
//
//  Created by <PERSON> on 18.3.25.
//  Copyright © 2025 CounterPath Corporation Inc. All rights reserved.
//

@testable import Bria
import Contacts
import XCTest

// MARK: - Mock Types

class MockCpcContact: CpcContact {
	private let mockContactId: String?
	private var mockPhoneNumbers: [CNLabeledValue<CNPhoneNumber>]?

	override var identifier: String? {
		return mockContactId
	}

	override var phoneNumbers: [CNLabeledValue<CNPhoneNumber>]? {
		get {
			return mockPhoneNumbers
		}
		set {
			mockPhoneNumbers = newValue
		}
	}

	override init() {
		mockContactId = nil
		mockPhoneNumbers = []
		super.init()
	}

	init(contactId: String) {
		mockContactId = contactId
		mockPhoneNumbers = []
		super.init()
	}
}

class MockCpcContactPreview: CpcContactPreview {
	private let mockContactId: String?
	private let mockContactInternalId: CpcContactInternalId?

	override var identifier: String? {
		return mockContactId
	}

	init(contactId: String?, contactInternalId: CpcContactInternalId?) {
		mockContactId = contactId
		mockContactInternalId = contactInternalId
		super.init()
	}
}

struct CpcContactInternalId {
	let contactId: String
}

final class PhoneIntegrationTests: XCTestCase {
	func test_withEmptyNumberInput_hidesAddNumberButton() {
		let sut = makeSUT(dependency: .init(isAddNumberFeatureEnabled: true))

		XCTAssertTrue(sut.isAddNumberButtonHidden())
	}

	func test_withNumberInput_showsAddNumberButton() {
		let sut = makeSUT(dependency: .init(isAddNumberFeatureEnabled: true))

		sut.simulateNumberInput("123")

		XCTAssertFalse(sut.isAddNumberButtonHidden())
	}

	func test_tapOnButtonAddNumber_showsContextMenu() {
		let sut = makeSUT(dependency: .init(isAddNumberFeatureEnabled: true))

		XCTAssertNil(sut.currentActionSheet)

		sut.simulateNumberInput("123")
		sut.simulateAddNumberAction()

		assertAddNumberContextMenu(for: sut)
	}

	// MARK: - Delete Button Tests

	func test_deleteButtonPressed_withEmptyString_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.callNumberInput.text = ""

		sut.simulateDeleteButtonPress()
	}

	func test_deleteButtonPressed_withSingleCharacter_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.callNumberInput.text = "1"
		sut.simulateDeleteButtonPress()

		XCTAssertNotNil(sut.callNumberInput.text)
	}

	func test_deleteButtonPressed_withSingleCharacterAsFirstResponder_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.callNumberInput.text = "1"
		sut.callNumberInput.becomeFirstResponder()

		sut.setCursorPosition(at: 1)
		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "")
	}

	func test_deleteButtonPressed_withProperlySetSelectedRange_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("123")

		sut.setCursorPosition(at: 3)
		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "12")
	}

	func test_deleteButtonPressed_withCursorAtBeginning_shouldDeleteCorrectly() {
		let sut = makeSUT()
		presentInWindow(sut)

		sut.simulateNumberInput("123")
		sut.setCursorPosition(at: 0)

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "123")

		dismiss(sut)
	}

	func test_deleteButtonPressed_withCursorInMiddle_shouldDeleteCorrectly() {
		let sut = makeSUT()
		presentInWindow(sut)

		sut.simulateNumberInput("12345")

		sut.setCursorPosition(at: 2)
		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "1345")

		dismiss(sut)
	}

	func test_deleteButtonPressed_withSelectedRange_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("12345")
		sut.setTextSelection(from: 1, to: 4)

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "15")
	}

	func test_deleteButtonPressed_withUnicodeCharacters_shouldDeleteCorrectly() {
		let sut = makeSUT()
		presentInWindow(sut)

		sut.simulateNumberInput("1👨2")
		sut.setCursorPosition(at: 2)

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "12")

		dismiss(sut)
	}

	func test_deleteButtonPressed_withJoinedUnicodeCharacters_shouldDeleteCorrectly() {
		let sut = makeSUT()
		presentInWindow(sut)

		sut.simulateNumberInput("123👨‍👩‍👧‍👦456")
		sut.setCursorPosition(at: 8)

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "123👨‍👩‍👧‍👦456")

		dismiss(sut)
	}

	func test_deleteButtonPressed_edgeCaseInvalidCursorPosition_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("1")
		sut.setCursorPosition(at: 2)

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "")
	}

	func test_deleteButtonPressed_rapidMultipleDeletes_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("12345")
		sut.setCursorPosition(at: 5)

		sut.simulateDeleteButtonPress() // "1234"
		sut.simulateDeleteButtonPress() // "123"
		sut.simulateDeleteButtonPress() // "12"
		sut.simulateDeleteButtonPress() // "1"
		sut.simulateDeleteButtonPress() // ""
		sut.simulateDeleteButtonPress() // should not crash on empty string

		XCTAssertEqual(sut.callNumberInput.text, "")
	}

	func test_deleteButtonPressed_concurrentTextChanges_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("123")
		sut.setCursorPosition(at: 3)

		sut.simulateDeleteButtonPress()

		sut.simulateNumberInput("")

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "")
	}

	func test_deleteButtonPressed_withNilSelectedTextRange_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("123")

		sut.callNumberInput.selectedTextRange = nil

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "12")
	}

	// MARK: - Special new tests

	func test_addToExistingContact_tableViewCommitEditingWithNilIndexPath_shouldCrash() {
		// This test reproduces the crash from BRIOS-2723 Sentry issue:
		// NSInvalidArgumentException: *** -[__NSPlaceholderArray initWithObjects:count:]: attempt to insert nil object from objects[0]
		// The crash occurs in ContactProfileViewController.tableView:commitEditingStyle:forRowAtIndexPath: at line 1863
		// when indexPath is nil and passed to @[indexPath] array creation

		let sut = makeSUT(dependency: .init(isAddNumberFeatureEnabled: true))

		// Simulate the "Add to Existing Contact" flow
		sut.simulateNumberInput("1234567890")
		sut.simulateAddNumberAction()

		// Create a mock ContactProfileViewController to simulate the crash scenario
		let contactProfileVC = ContactProfileViewController()
		let mockContact = MockCpcContact(contactId: "test-contact-id")
		contactProfileVC.contact = mockContact
		contactProfileVC.allowsEditing = true
		contactProfileVC.isEditing = true

		// Load the view to initialize the table view
		contactProfileVC.loadViewIfNeeded()

		// This should crash when indexPath is nil - reproducing the Sentry crash
		// The crash happens because @[indexPath] tries to create an array with nil object
		XCTAssertThrowsError(try {
			// Simulate the problematic call with nil indexPath that causes the crash
			contactProfileVC.tableView(contactProfileVC.tableView, commitEditingStyle: .delete, forRowAtIndexPath: nil)
		}()) { error in
			// Expect NSInvalidArgumentException when trying to create array with nil indexPath
			XCTAssertTrue(error.localizedDescription.contains("nil object") || error.localizedDescription.contains("NSInvalidArgumentException"))
		}
	}


}

// MARK: - Asserts

private extension PhoneIntegrationTests {
	func assertAddNumberContextMenu(
		for sut: PhoneViewController,
		file _: StaticString = #file,
		line _: UInt = #line
	) {
		XCTAssertNotNil(sut.currentActionSheet)

		let title = sut.currentActionSheet?.title
		XCTAssertEqual(title, NSLocalizedString("Add Number", comment: ""))
		let button1Text = sut.currentActionSheet?.buttons?.first?.text
		XCTAssertEqual(button1Text, NSLocalizedString("Create New Contact", comment: ""))
		let button2Text = sut.currentActionSheet?.buttons?[1].text
		XCTAssertEqual(button2Text, NSLocalizedString("Add to Existing Contact", comment: ""))
	}
}

// MARK: - System Under Tests (SUT) Factory

private extension PhoneIntegrationTests {
	func makeSUT(
		dependency: PhoneDependency = .init(),
		file: StaticString = #file,
		line: UInt = #line
	) -> PhoneViewController {
		let vc = PhoneViewController(dependency: dependency)

		assertNoMemoryLeaks(vc, file: file, line: line)

		vc.loadViewIfNeeded()

		return vc
	}

	func presentInWindow(_ sut: PhoneViewController) {
		let window = UIWindow(frame: UIScreen.main.bounds)
		window.rootViewController = sut
		window.makeKeyAndVisible()
	}

	func dismiss(_ sut: PhoneViewController) {
		sut.view.removeFromSuperview()

		if
			let windowScene = UIApplication.shared.connectedScenes
				.compactMap({ $0 as? UIWindowScene })
				.first(where: { $0.activationState == .foregroundActive }),
			let window = windowScene.windows.first(where: { $0.isKeyWindow }) {
			window.rootViewController = nil
			window.isHidden = true
		}

		delay(0.3)
	}

	func delay(_ seconds: TimeInterval) {
		RunLoop.current.run(until: Date(timeIntervalSinceNow: seconds))
	}
}

// MARK: - SUT Helpers

private extension PhoneViewController {
	func isAddNumberViewPresented() -> Bool {
		addNumberButton() != nil
	}

	func isAddNumberButtonHidden() -> Bool {
		addNumberButton()?.isHidden ?? false
	}

	func simulateNumberInput(_ number: String) {
		callNumberInput.text = number
	}

	func simulateAddNumberAction() {
		addNumberButton()?.sendActions(for: .touchUpInside)
	}

	func simulateKeyPadButtonPress(_ buttonId: ButtonId, character: unichar = 0) {
		buttonPressed(buttonId, forCharacter: character)
	}

	func simulateDeleteButtonPress() {
		buttonPressed(EButtonId_del, forCharacter: 0)
	}

	func setCursorPosition(at offset: Int) {
		setTextSelection(from: offset, to: offset)
	}

	func setTextSelection(from startOffset: Int, to endOffset: Int) {
		callNumberInput.becomeFirstResponder()
		if
			let startPosition = callNumberInput.position(
				from: callNumberInput.beginningOfDocument,
				offset: startOffset
			),
			let endPosition = callNumberInput.position(
				from: callNumberInput.beginningOfDocument,
				offset: endOffset
			) {
			callNumberInput.selectedTextRange = callNumberInput.textRange(
				from: startPosition,
				to: endPosition
			)
		}
	}

	private func addNumberButton() -> UIButton? {
		callNumberContainerView?.arrangedSubviews[0] as? UIButton
	}
}
